import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faInfoCircle, 
  faUsers, 
  faHome, 
  faCogs, 
  faCommentDots, 
  faChevronRight 
} from '@fortawesome/free-solid-svg-icons';

const HomePage = () => {
  return (
    <div className="p-4">
      <div className="bg-white rounded-lg shadow-md p-4 mb-4 flex items-center">
        <img
          src="https://design.gemcoder.com/staticResource/echoAiSystemImages/73f791ce8ee289e7e5fb89c3de5431f8.png"
          alt="用户头像"
          className="w-20 h-20 rounded-full object-cover mr-4"
        />
        <div>
          <h2 className="text-xl font-semibold text-gray-800">张三</h2>
          <p className="text-gray-600">普通用户</p>
        </div>
      </div>
      
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <a href="#" className="flex items-center p-4 border-b border-gray-200 hover:bg-gray-50">
          <FontAwesomeIcon icon={faInfoCircle} className="text-blue-500 mr-3" />
          <span className="text-gray-800 flex-1">我的信息</span>
          <FontAwesomeIcon icon={faChevronRight} className="text-gray-400" />
        </a>
        
        <a href="#" className="flex items-center p-4 border-b border-gray-200 hover:bg-gray-50">
          <FontAwesomeIcon icon={faUsers} className="text-green-500 mr-3" />
          <span className="text-gray-800 flex-1">我的族谱</span>
          <FontAwesomeIcon icon={faChevronRight} className="text-gray-400" />
        </a>
        
        <a href="#" className="flex items-center p-4 border-b border-gray-200 hover:bg-gray-50">
          <FontAwesomeIcon icon={faHome} className="text-purple-500 mr-3" />
          <span className="text-gray-800 flex-1">我的家谱</span>
          <FontAwesomeIcon icon={faChevronRight} className="text-gray-400" />
        </a>
        
        <a href="#" className="flex items-center p-4 border-b border-gray-200 hover:bg-gray-50">
          <FontAwesomeIcon icon={faCogs} className="text-orange-500 mr-3" />
          <span className="text-gray-800 flex-1">设置关系称谓</span>
          <FontAwesomeIcon icon={faChevronRight} className="text-gray-400" />
        </a>
        
        <a href="#" className="flex items-center p-4 hover:bg-gray-50">
          <FontAwesomeIcon icon={faCommentDots} className="text-red-500 mr-3" />
          <span className="text-gray-800 flex-1">意见反馈 (联系我们)</span>
          <FontAwesomeIcon icon={faChevronRight} className="text-gray-400" />
        </a>
      </div>
    </div>
  );
};

export default HomePage; 