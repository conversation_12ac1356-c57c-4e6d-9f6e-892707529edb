import React, { useState } from 'react';
import FamilyTreeComponent from '../components/FamilyTreeComponent';

// 算法说明图
const algorithmDiagram = `graph TD
    classDef male fill:#ebf8ff,stroke:#3b82f6,stroke-width:2px
    classDef female fill:#fdf2f8,stroke:#ec4899,stroke-width:2px
    classDef current fill:#ebf8ff,stroke:#3b82f6,stroke-width:4px
    
    subgraph "算法流程"
        A[自下而上计算]:::box --> B["1. 计算叶子节点位置<br/>(无子女的节点)"]
        B --> C["2. 计算父节点位置<br/>(子节点位置的平均值)"]
        C --> D["3. 计算配偶节点位置<br/>(在父节点右侧)"]
    end
    
    subgraph "父亲节点位置计算示例"
        E["儿子<br/>X=100"]:::male --> G["父亲<br/>X=(100+200)/2=150"]:::male
        F["女儿<br/>X=200"]:::female --> G
        G --> H["母亲<br/>X=150+80+20/2=240"]:::female
    end
    
    subgraph "节点间距规则"
        I["节点宽度:<br/>80px"]:::box --> J["子节点间距:<br/>20px"]:::box
        J --> K["夫妻节点间距:<br/>20px"]:::box
    end

    classDef box fill:#f9f9f9,stroke:#999,stroke-width:1px,color:#333`;

const FamilyTreePage = () => {
  const [scale, setScale] = useState(1);
  const [showInfo, setShowInfo] = useState(false);
  const [showDiagram, setShowDiagram] = useState(false);

  const handleZoomIn = () => {
    setScale(prev => Math.min(prev + 0.1, 2));
  };

  const handleZoomOut = () => {
    setScale(prev => Math.max(prev - 0.1, 0.5));
  };

  const handleResetZoom = () => {
    setScale(1);
  };

  const toggleInfo = () => {
    setShowInfo(!showInfo);
  };

  const toggleDiagram = () => {
    setShowDiagram(!showDiagram);
  };

  return (
    <div className="p-4">
      <div className="bg-white rounded-lg shadow-md p-4 mb-4">
        <div className="flex justify-between items-center mb-2">
          <h2 className="text-xl font-semibold text-gray-800">家族树视图</h2>
          <div className="flex space-x-4">
            <button 
              onClick={toggleInfo} 
              className="text-blue-500 text-sm hover:underline"
            >
              {showInfo ? '隐藏说明' : '查看说明'}
            </button>
            <button 
              onClick={toggleDiagram} 
              className="text-green-500 text-sm hover:underline"
            >
              {showDiagram ? '隐藏算法图' : '查看算法图'}
            </button>
          </div>
        </div>
        
        {showInfo && (
          <div className="bg-blue-50 p-3 rounded-md mb-4 text-sm">
            <h3 className="font-medium text-blue-700 mb-1">实现算法说明</h3>
            <ul className="list-disc list-inside text-blue-800 space-y-1">
              <li>采用<strong>子女位置决定父亲位置</strong>的算法实现</li>
              <li>父节点的横坐标为其所有子节点横坐标的平均值</li>
              <li>节点宽度为80px，子节点间距为20px，夫妻节点间距为20px</li>
              <li>使用后序遍历计算位置：先计算最底层子节点位置，再向上计算父节点</li>
              <li>叶子节点（无子女的节点）直接从左到右排列</li>
              <li>父节点的纵坐标由层级确定，同一代的节点在同一水平线上</li>
            </ul>
          </div>
        )}
        
        {showDiagram && (
          <div className="bg-gray-50 p-3 rounded-md mb-4">
            <h3 className="font-medium text-gray-700 mb-2">算法原理图解</h3>
            <div className="w-full bg-white border border-gray-200 rounded p-2">
              <pre className="text-xs overflow-auto">{algorithmDiagram}</pre>
              <p className="mt-2 text-xs text-gray-500 text-center">说明：该图展示了如何根据子节点位置计算父节点位置的原理</p>
            </div>
          </div>
        )}
        
        <p className="text-sm text-gray-600 mb-4">
          该家族树展示了父亲、母亲、本人、妻子、子女和兄弟的关系结构。父亲节点的位置由其所有子女节点位置的平均值确定。
        </p>
        
        {/* 缩放控制 */}
        <div className="zoom-controls flex justify-center space-x-4 mb-4">
          <button 
            onClick={handleZoomOut}
            className="px-3 py-1 bg-gray-200 rounded-md text-gray-700 hover:bg-gray-300"
          >
            -
          </button>
          <button 
            onClick={handleResetZoom}
            className="px-3 py-1 bg-gray-200 rounded-md text-gray-700 hover:bg-gray-300"
          >
            重置
          </button>
          <button 
            onClick={handleZoomIn}
            className="px-3 py-1 bg-gray-200 rounded-md text-gray-700 hover:bg-gray-300"
          >
            +
          </button>
          <span className="text-sm text-gray-500 py-1">
            {Math.round(scale * 100)}%
          </span>
        </div>

        <div className="family-tree-wrapper overflow-auto border border-gray-200 rounded-lg">
          <div 
            className="family-tree-container" 
            style={{
              transform: `scale(${scale})`,
              transformOrigin: 'center top',
              transition: 'transform 0.3s ease',
              minHeight: '60vh'
            }}
          >
            <FamilyTreeComponent />
          </div>
        </div>
        
        <div className="mt-4 text-xs text-gray-500">
          <p>注：蓝色边框表示男性，粉色边框表示女性，粗边框表示当前本人。</p>
        </div>
      </div>
    </div>
  );
};

export default FamilyTreePage; 
 