import React, { useEffect, useState } from 'react';

const NODE_WIDTH = 80;
const NODE_SPACING = 20;

// Family tree data structure with avatars
const familyTreeData = {
  id: 'father',
  name: '父亲',
  avatar: 'https://design.gemcoder.com/staticResource/echoAiSystemImages/7549e2f2b02d82f1e8470f435e31608a.png',
  gender: 'male',
  spouse: { 
    id: 'mother', 
    name: '母亲', 
    avatar: 'https://design.gemcoder.com/staticResource/echoAiSystemImages/d60faf2c04fda31d117381f7baa265ef.png',
    gender: 'female'
  },
  children: [
    {
      id: 'self',
      name: '本人',
      avatar: 'https://design.gemcoder.com/staticResource/echoAiSystemImages/298641ab193431bf14d49fada4e0e88b.png',
      gender: 'male',
      isCurrent: true,
      spouse: { 
        id: 'wife', 
        name: '妻子', 
        avatar: 'https://design.gemcoder.com/staticResource/echoAiSystemImages/7977957afe4739ec843dfcf1eb5487d1.png',
        gender: 'female'
      },
      children: [
        { 
          id: 'son', 
          name: '儿子', 
          avatar: 'https://design.gemcoder.com/staticResource/echoAiSystemImages/d08e0d218ba20d96dcfeb92417172d8c.png',
          gender: 'male',
          children: [] 
        },
        { 
          id: 'daughter', 
          name: '女儿', 
          avatar: 'https://design.gemcoder.com/staticResource/echoAiSystemImages/2a3a25c62b3f4437fb8a79dd6791373c.png',
          gender: 'female',
          children: [] 
        },
      ]
    },
    {
      id: 'brother',
      name: '哥哥',
      avatar: 'https://design.gemcoder.com/staticResource/echoAiSystemImages/1036d4c7fdf61e73f5b8109910b79ef6.png',
      gender: 'male',
      spouse: { 
        id: 'sister-in-law', 
        name: '嫂子', 
        avatar: 'https://design.gemcoder.com/staticResource/echoAiSystemImages/d60faf2c04fda31d117381f7baa265ef.png',
        gender: 'female'
      },
      children: [
        { 
          id: 'nephew', 
          name: '侄子', 
          avatar: 'https://design.gemcoder.com/staticResource/echoAiSystemImages/7549e2f2b02d82f1e8470f435e31608a.png',
          gender: 'male',
          children: [] 
        },
        { 
          id: 'niece', 
          name: '侄女', 
          avatar: 'https://design.gemcoder.com/staticResource/echoAiSystemImages/2a3a25c62b3f4437fb8a79dd6791373c.png',
          gender: 'female',
          children: [] 
        },
      ]
    },
  ]
};

// Calculate positions for the entire tree
const calculateTreePositions = (tree) => {
  const positions = {};
  
  // Post-order traversal to calculate positions from bottom up
  const calculatePositions = (node, level = 0, startX = 0) => {
    if (!node) return { width: 0, center: 0 };
    
    // Handle leaf nodes (no children)
    if (!node.children || node.children.length === 0) {
      positions[node.id] = { 
        x: startX + NODE_WIDTH / 2, 
        y: level * (NODE_WIDTH + NODE_SPACING * 2),
        level,
        isLeaf: true // 标记为叶子节点
      };
      return { width: NODE_WIDTH, center: positions[node.id].x };
    }
    
    // Process all children first
    let totalWidth = 0;
    let childrenCenters = [];
    
    for (let i = 0; i < node.children.length; i++) {
      const child = node.children[i];
      // Add spacing between siblings
      if (i > 0) totalWidth += NODE_SPACING;
      
      const childResult = calculatePositions(child, level + 1, startX + totalWidth);
      totalWidth += childResult.width;
      childrenCenters.push(childResult.center);
    }
    
    // Calculate parent position based on children's positions (average of first and last child)
    const firstChildX = childrenCenters[0];
    const lastChildX = childrenCenters[childrenCenters.length - 1];
    const centerX = (firstChildX + lastChildX) / 2;
    
    positions[node.id] = { 
      x: centerX, 
      y: level * (NODE_WIDTH + NODE_SPACING * 2),
      level,
      isLeaf: false // 标记为非叶子节点
    };
    
    // If spouse exists, position next to the person
    if (node.spouse) {
      positions[node.spouse.id] = {
        x: centerX + NODE_WIDTH + NODE_SPACING / 2, // Add spacing for the connector
        y: level * (NODE_WIDTH + NODE_SPACING * 2),
        level,
        isLeaf: false // 妻子节点也标记为非叶子节点
      };
    }
    
    return { width: totalWidth, center: centerX };
  };
  
  calculatePositions(tree);
  return positions;
};

const FamilyTreeComponent = () => {
  const [positions, setPositions] = useState({});
  
  useEffect(() => {
    const treePositions = calculateTreePositions(familyTreeData);
    setPositions(treePositions);
  }, []);
  
  // Render family node
  const renderFamilyNode = (node, isSpouse = false) => {
    if (!node || !positions[node.id]) return null;
    
    const pos = positions[node.id];
    const isCurrent = node.isCurrent;
    const isMale = node.gender === 'male';
    const isWife = isSpouse && node.gender === 'female'; // 判断是否为妻子节点
    const isFirstLevel = pos.level === 0; // 判断是否为第一层节点
    const isLeafNode = pos.isLeaf; // 判断是否为叶子节点
    
    const nodeStyle = {
      position: 'absolute',
      left: `${pos.x - NODE_WIDTH/2}px`,
      top: `${pos.y}px`,
      width: `${NODE_WIDTH}px`,
      textAlign: 'center',
      zIndex: 2
    };
    
    const borderColor = isMale ? '#3b82f6' : '#ec4899';
    const borderWidth = isCurrent ? '3px' : '2px';
    
    // 为非第一层且非妻子节点添加上边框中间点向上的20px线段
    const topConnectorLineStyle = !isWife && !isFirstLevel ? {
      position: 'absolute',
      left: '50%',
      top: '-20px', // 向上延伸20px
      width: '2px',
      height: '20px',
      backgroundColor: '#a0aec0',
      transform: 'translateX(-50%)' // 确保水平居中
    } : null;
    
    // 为有儿女的男性节点图片下方添加20px向下线段
    const bottomImageConnectorLineStyle = isMale && !isLeafNode && (node.children && node.children.length > 0) ? {
      position: 'absolute',
      left: '50%',
      top: `${NODE_WIDTH}px`, // 从图片底部开始
      width: '2px',
      height: '20px',
      backgroundColor: '#a0aec0',
      transform: 'translateX(-50%)' // 确保水平居中
    } : null;
    
    return (
      <div key={node.id} style={nodeStyle}>
        {!isWife && !isFirstLevel && <div style={topConnectorLineStyle}></div>}
        <div style={{ position: 'relative' }}>
          <img
            src={node.avatar}
            alt={`${node.name} 头像`}
            className={`rounded-full mx-auto object-cover border-${isCurrent ? '3' : '2'}`}
            style={{
              width: `${NODE_WIDTH}px`,
              height: `${NODE_WIDTH}px`,
              borderColor: borderColor,
              borderWidth: borderWidth
            }}
          />
          {isMale && !isLeafNode && (node.children && node.children.length > 0) && <div style={bottomImageConnectorLineStyle}></div>}
        </div>
        <p className={`text-sm mt-1 font-${isCurrent ? 'semibold' : 'medium'}`} style={{ marginTop: '5px' }}>
          {node.name}
        </p>
      </div>
    );
  };
  
  // Render spouse connector
  const renderSpouseConnector = (node) => {
    if (!node || !node.spouse || !positions[node.id] || !positions[node.spouse.id]) return null;
    
    const pos = positions[node.id];
    
    // 夫妻连接线保持在节点中间的水平位置
    const connectorStyle = {
      position: 'absolute',
      left: `${pos.x + NODE_WIDTH/2}px`,
      top: `${pos.y + NODE_WIDTH/2}px`,
      width: `${NODE_SPACING}px`,
      height: '2px',
      backgroundColor: '#a0aec0',
      zIndex: 1
    };
    
    return <div key={`${node.id}-${node.spouse.id}-connector`} style={connectorStyle} />;
  };
  
  // Render parent-child connector
  const renderParentChildConnector = (parent, child) => {
    if (!parent || !child || !positions[parent.id] || !positions[child.id]) return null;
    
    const parentPos = positions[parent.id];
    const childPos = positions[child.id];
    const isChildWife = child.gender === 'female' && child.id.includes('wife'); // 检查子节点是否为妻子
    const isParentWife = parent.gender === 'female' && parent.id.includes('wife'); // 检查父节点是否为妻子
    const isParentMale = parent.gender === 'male';
    const isParentLeaf = parentPos.isLeaf;
    const isFirstLevel = childPos.level === 1; // 子节点是第一层的子节点
    
    // 垂直连接线的起始位置考虑父节点是否为男性且非叶子节点
    const verticalLineStartY = isParentWife 
      ? parentPos.y + NODE_WIDTH + 5 // 妻子节点从节点底部+文本空间开始
      : isParentMale && !isParentLeaf
        ? parentPos.y + NODE_WIDTH + 5 + 20 // 非叶子男性节点从图片底部+线段+文本空间开始
        : parentPos.y + NODE_WIDTH + 5; // 其他节点从节点底部+文本空间开始
    
    // Vertical line from parent
    const verticalLineStyle = {
      position: 'absolute',
      left: `${parentPos.x}px`,
      top: `${verticalLineStartY}px`,
      width: '2px',
      // 连接线高度考虑子节点是否为第一层或妻子节点
      height: `${childPos.y - verticalLineStartY - (isChildWife || isFirstLevel ? 0 : 20)}px`,
      backgroundColor: '#a0aec0',
      zIndex: 1
    };
    
    // Horizontal line to child if needed
    let horizontalLineStyle = null;
    if (Math.abs(parentPos.x - childPos.x) > 2) { // Only if there's a significant horizontal difference
      const left = Math.min(parentPos.x, childPos.x);
      const width = Math.abs(childPos.x - parentPos.x);
      
      horizontalLineStyle = {
        position: 'absolute',
        left: `${left}px`,
        // 水平线的位置考虑子节点是否为第一层或妻子节点
        top: `${childPos.y - (isChildWife || isFirstLevel ? NODE_SPACING : 20)}px`,
        width: `${width}px`,
        height: '2px',
        backgroundColor: '#a0aec0',
        zIndex: 1
      };
    }
    
    return (
      <React.Fragment key={`${parent.id}-${child.id}-connector`}>
        <div style={verticalLineStyle} />
        {horizontalLineStyle && <div style={horizontalLineStyle} />}
      </React.Fragment>
    );
  };
  
  // Recursively render the family tree
  const renderTree = (node) => {
    if (!node) return null;
    
    // Render current node and its spouse
    const elements = [
      renderFamilyNode(node),
      node.spouse && renderFamilyNode(node.spouse, true),
      node.spouse && renderSpouseConnector(node)
    ];
    
    // Render children and connections
    if (node.children && node.children.length > 0) {
      node.children.forEach(child => {
        elements.push(renderTree(child));
        elements.push(renderParentChildConnector(node, child));
      });
    }
    
    return elements;
  };
  
  return (
    <div style={{ position: 'relative', padding: '40px', minHeight: '500px' }}>
      {Object.keys(positions).length > 0 && renderTree(familyTreeData)}
    </div>
  );
};

export default FamilyTreeComponent; 