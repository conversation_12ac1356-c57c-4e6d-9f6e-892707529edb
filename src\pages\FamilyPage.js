import React, { useState, useEffect, useRef } from 'react';
import * as echarts from 'echarts';

const FamilyPage = () => {
  const [activeView, setActiveView] = useState('graph');
  const chartRef = useRef(null);
  const chartInstance = useRef(null);
  
  useEffect(() => {
    // 当视图为图结构且DOM已挂载时初始化图表
    if (activeView === 'graph' && chartRef.current) {
      initFamilyChart();
    }
    
    // 组件卸载时销毁图表实例
    return () => {
      if (chartInstance.current) {
        chartInstance.current.dispose();
      }
    };
  }, [activeView]);
  
  // 窗口大小变化时重新渲染图表
  useEffect(() => {
    const handleResize = () => {
      if (chartInstance.current) {
        chartInstance.current.resize();
      }
    };
    
    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);
  
  const switchView = (viewType) => {
    setActiveView(viewType);
  };
  
  const initFamilyChart = () => {
    // 如果已经有实例，先销毁
    if (chartInstance.current) {
      chartInstance.current.dispose();
    }
    
    // 初始化图表
    chartInstance.current = echarts.init(chartRef.current);
    
    // 模拟数据
    const data = [
      {
        name: '本人',
        symbolSize: 60,
        category: 0,
        itemStyle: {
          color: '#4299e1'
        },
        label: {
          formatter: '{b}\n{avatar|}',
          rich: {
            avatar: {
              backgroundColor: {
                image: 'https://design.gemcoder.com/staticResource/echoAiSystemImages/298641ab193431bf14d49fada4e0e88b.png'
              },
              width: 60,
              height: 60,
              borderRadius: 30
            }
          }
        }
      }, 
      {
        name: '父亲',
        symbolSize: 50,
        category: 1,
        itemStyle: {
          color: '#63b3ed'
        },
        label: {
          formatter: '{b}\n{avatar|}',
          rich: {
            avatar: {
              backgroundColor: {
                image: 'https://design.gemcoder.com/staticResource/echoAiSystemImages/7549e2f2b02d82f1e8470f435e31608a.png'
              },
              width: 50,
              height: 50,
              borderRadius: 25
            }
          }
        }
      },
      {
        name: '母亲',
        symbolSize: 50,
        category: 1,
        itemStyle: {
          color: '#63b3ed'
        },
        label: {
          formatter: '{b}\n{avatar|}',
          rich: {
            avatar: {
              backgroundColor: {
                image: 'https://design.gemcoder.com/staticResource/echoAiSystemImages/d60faf2c04fda31d117381f7baa265ef.png'
              },
              width: 50,
              height: 50,
              borderRadius: 25
            }
          }
        }
      },
      {
        name: '妻子',
        symbolSize: 50,
        category: 2,
        itemStyle: {
          color: '#ed64a6'
        },
        label: {
          formatter: '{b}\n{avatar|}',
          rich: {
            avatar: {
              backgroundColor: {
                image: 'https://design.gemcoder.com/staticResource/echoAiSystemImages/7977957afe4739ec843dfcf1eb5487d1.png'
              },
              width: 50,
              height: 50,
              borderRadius: 25
            }
          }
        }
      },
      {
        name: '儿子',
        symbolSize: 40,
        category: 3,
        itemStyle: {
          color: '#48bb78'
        },
        label: {
          formatter: '{b}\n{avatar|}',
          rich: {
            avatar: {
              backgroundColor: {
                image: 'https://design.gemcoder.com/staticResource/echoAiSystemImages/d08e0d218ba20d96dcfeb92417172d8c.png'
              },
              width: 40,
              height: 40,
              borderRadius: 20
            }
          }
        }
      },
      {
        name: '女儿',
        symbolSize: 40,
        category: 3,
        itemStyle: {
          color: '#48bb78'
        },
        label: {
          formatter: '{b}\n{avatar|}',
          rich: {
            avatar: {
              backgroundColor: {
                image: 'https://design.gemcoder.com/staticResource/echoAiSystemImages/2a3a25c62b3f4437fb8a79dd6791373c.png'
              },
              width: 40,
              height: 40,
              borderRadius: 20
            }
          }
        }
      },
      {
        name: '哥哥',
        symbolSize: 45,
        category: 4,
        itemStyle: {
          color: '#ecc94b'
        },
        label: {
          formatter: '{b}\n{avatar|}',
          rich: {
            avatar: {
              backgroundColor: {
                image: 'https://design.gemcoder.com/staticResource/echoAiSystemImages/1036d4c7fdf61e73f5b8109910b79ef6.png'
              },
              width: 45,
              height: 45,
              borderRadius: 22.5
            }
          }
        }
      },
      {
        name: '妹妹',
        symbolSize: 45,
        category: 4,
        itemStyle: {
          color: '#ecc94b'
        },
        label: {
          formatter: '{b}\n{avatar|}',
          rich: {
            avatar: {
              backgroundColor: {
                image: 'https://design.gemcoder.com/staticResource/echoAiSystemImages/81333193e6169cb145fdabcb6a26ec40.png'
              },
              width: 45,
              height: 45,
              borderRadius: 22.5
            }
          }
        }
      }
    ];
    
    const links = [
      {
        source: '本人',
        target: '父亲',
        lineStyle: {
          color: '#a0aec0',
          width: 2
        }
      },
      {
        source: '本人',
        target: '母亲',
        lineStyle: {
          color: '#a0aec0',
          width: 2
        }
      },
      {
        source: '本人',
        target: '妻子',
        lineStyle: {
          color: '#ed64a6',
          width: 2,
          curveness: 0.2
        }
      },
      {
        source: '本人',
        target: '儿子',
        lineStyle: {
          color: '#a0aec0',
          width: 2
        }
      },
      {
        source: '本人',
        target: '女儿',
        lineStyle: {
          color: '#a0aec0',
          width: 2
        }
      },
      {
        source: '本人',
        target: '哥哥',
        lineStyle: {
          color: '#a0aec0',
          width: 2
        }
      },
      {
        source: '本人',
        target: '妹妹',
        lineStyle: {
          color: '#a0aec0',
          width: 2
        }
      }
    ];
    
    const categories = [
      { name: '本人' },
      { name: '父母' },
      { name: '配偶' },
      { name: '子女' },
      { name: '兄弟姐妹' }
    ];
    
    const option = {
      tooltip: {
        formatter: function(params) {
          if (params.dataType === 'node') {
            return params.name;
          }
          return '';
        }
      },
      legend: [{
        data: categories.map(function(a) {
          return a.name;
        }),
        bottom: 0,
        left: 'center',
        itemGap: 10,
        textStyle: {
          color: '#333'
        }
      }],
      series: [{
        type: 'graph',
        layout: 'force',
        data: data,
        links: links,
        categories: categories,
        roam: true, // 开启缩放和平移
        label: {
          show: true,
          position: 'bottom',
          formatter: '{b}',
          color: '#333',
          fontSize: 12
        },
        force: {
          repulsion: 1000, // 节点之间的斥力
          edgeLength: [100, 150], // 边的长度范围
          layoutAnimation: false // 关闭布局动画，避免频繁重绘
        },
        lineStyle: {
          opacity: 0.9,
          width: 2,
          curveness: 0 // 默认直线
        },
        emphasis: {
          focus: 'adjacency',
          lineStyle: {
            width: 5
          }
        }
      }]
    };
    
    // 设置图表配置项
    chartInstance.current.setOption(option);
  };
  
  return (
    <div className="p-4">
      <div className="bg-white rounded-lg shadow-md p-4 mb-4">
        <h2 className="text-xl font-semibold text-gray-800 mb-2">家谱视图</h2>
        <div className="flex justify-around mb-4">
          <button
            className={`px-4 py-2 rounded-md text-sm ${
              activeView === 'graph' ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700'
            }`}
            onClick={() => switchView('graph')}
          >
            图结构
          </button>
          <button
            className={`px-4 py-2 rounded-md text-sm ${
              activeView === 'table' ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700'
            }`}
            onClick={() => switchView('table')}
          >
            年代表格
          </button>
        </div>
        
        {/* 家谱图结构 */}
        {activeView === 'graph' && (
          <div className="family-view">
            <div className="relative w-full h-96 flex items-center justify-center">
              {/* ECharts 图表容器 */}
              <div ref={chartRef} className="w-full h-full"></div>
            </div>
          </div>
        )}
        
        {/* 家谱年代表格 */}
        {activeView === 'table' && (
          <div className="family-view">
            <h3 className="text-lg font-medium mb-2">按年代显示</h3>
            <div className="bg-gray-50 p-3 rounded-md mb-2">
              <h4 className="font-semibold text-gray-700">
                1990年代前期 (1990-1993)
              </h4>
              <ul className="list-disc list-inside text-sm text-gray-600 mt-1">
                <li>张小明 (1992)</li>
                <li>李芳 (1993)</li>
              </ul>
            </div>
            
            <div className="bg-gray-50 p-3 rounded-md mb-2">
              <h4 className="font-semibold text-gray-700">
                1990年代中期 (1994-1996)
              </h4>
              <ul className="list-disc list-inside text-sm text-gray-600 mt-1">
                <li>王刚 (1994)</li>
              </ul>
            </div>
            
            <div className="bg-gray-50 p-3 rounded-md">
              <h4 className="font-semibold text-gray-700">
                1990年代后期 (1997-1999)
              </h4>
              <ul className="list-disc list-inside text-sm text-gray-600 mt-1">
                <li>陈华 (1997)</li>
              </ul>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default FamilyPage; 