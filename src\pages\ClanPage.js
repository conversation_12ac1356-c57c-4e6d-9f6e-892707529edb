import React, { useState, useEffect } from 'react';

const NODE_WIDTH = 80;     // 节点宽度
const NODE_HEIGHT = 85;    // 节点高度
const NODE_SPACING = 20;   // 水平节点间距
const VERTICAL_SPACING = 40; // 上下层之间的垂直间距 - 父子节点之间间隔40px
const IMAGE_SIZE = 65;     // 图片尺寸

// 家族树数据结构
const familyTreeData = {
  id: 'grandfather',
  name: '祖父',
  avatar: 'https://design.gemcoder.com/staticResource/echoAiSystemImages/4e5e35ad7d62078b7f0cac729083337c.png',
  gender: 'male',
  spouse: {
    id: 'grandmother',
    name: '祖母',
    avatar: 'https://design.gemcoder.com/staticResource/echoAiSystemImages/73c773ac08612ed4c2d709b0e76660d0.png',
    gender: 'female'
  },
  children: [
    {
      id: 'father',
      name: '父亲',
      avatar: 'https://design.gemcoder.com/staticResource/echoAiSystemImages/d926e30270a7d638cd91eacd8bb56872.png',
      gender: 'male',
      spouse: {
        id: 'mother',
        name: '母亲',
        avatar: 'https://design.gemcoder.com/staticResource/echoAiSystemImages/735bcb246e42b9c2dc8e3e35b17aa838.png',
        gender: 'female'
      },
      children: [
        {
          id: 'self',
          name: '本人',
          avatar: 'https://design.gemcoder.com/staticResource/echoAiSystemImages/298641ab193431bf14d49fada4e0e88b.png',
          gender: 'male',
          isCurrent: true,
          spouse: {
            id: 'wife',
            name: '妻子',
            avatar: 'https://design.gemcoder.com/staticResource/echoAiSystemImages/489e17e1cbee6bb62ec2e5a4f34e98b6.png',
            gender: 'female'
          },
          children: [
            {
              id: 'son',
              name: '儿子',
              avatar: 'https://design.gemcoder.com/staticResource/echoAiSystemImages/d08e0d218ba20d96dcfeb92417172d8c.png',
              gender: 'male',
              children: []
            },
            {
              id: 'daughter',
              name: '女儿',
              avatar: 'https://design.gemcoder.com/staticResource/echoAiSystemImages/2a3a25c62b3f4437fb8a79dd6791373c.png',
              gender: 'female',
              children: []
            }
          ]
        },
        {
          id: 'brother',
          name: '哥哥',
          avatar: 'https://design.gemcoder.com/staticResource/echoAiSystemImages/1036d4c7fdf61e73f5b8109910b79ef6.png',
          gender: 'male',
          spouse: {
            id: 'sister-in-law',
            name: '嫂子',
            avatar: 'https://design.gemcoder.com/staticResource/echoAiSystemImages/d60faf2c04fda31d117381f7baa265ef.png',
            gender: 'female'
          },
          children: [
            {
              id: 'nephew',
              name: '侄子',
              avatar: 'https://design.gemcoder.com/staticResource/echoAiSystemImages/7549e2f2b02d82f1e8470f435e31608a.png',
              gender: 'male',
              children: []
            },
            {
              id: 'niece',
              name: '侄女',
              avatar: 'https://design.gemcoder.com/staticResource/echoAiSystemImages/2a3a25c62b3f4437fb8a79dd6791373c.png',
              gender: 'female',
              children: []
            }
          ]
        }
      ]
    },
    {
      id: 'uncle',
      name: '叔叔',
      avatar: 'https://design.gemcoder.com/staticResource/echoAiSystemImages/7549e2f2b02d82f1e8470f435e31608a.png',
      gender: 'male',
      spouse: {
        id: 'aunt',
        name: '婶婶',
        avatar: 'https://design.gemcoder.com/staticResource/echoAiSystemImages/d60faf2c04fda31d117381f7baa265ef.png',
        gender: 'female'
      },
      children: [
        {
          id: 'cousin-girl',
          name: '堂妹',
          avatar: 'https://design.gemcoder.com/staticResource/echoAiSystemImages/2a3a25c62b3f4437fb8a79dd6791373c.png',
          gender: 'female',
          children: []
        }
      ]
    }
  ]
};

// 计算族谱树中所有节点的位置
const calculateTreePositions = (tree) => {
  const positions = {};
  
  // 使用后序遍历（自下而上）计算位置
  const calculatePositions = (node, level = 0, startX = 0) => {
    if (!node) return { width: 0, center: 0 };
    
    // 处理叶子节点（没有子女的节点）
    if (!node.children || node.children.length === 0) {
      positions[node.id] = { 
        x: startX + NODE_WIDTH / 2, 
        y: level * (NODE_HEIGHT + VERTICAL_SPACING), // 使用固定的垂直间距
        level,
        isLeaf: true // 标记为叶子节点
      };
      return { width: NODE_WIDTH, center: positions[node.id].x };
    }
    
    // 先处理所有子节点
    let totalWidth = 0;
    let childrenCenters = [];
    let childrenIds = [];
    
    for (let i = 0; i < node.children.length; i++) {
      const child = node.children[i];
      // 添加兄弟节点间的间距
      if (i > 0) totalWidth += NODE_SPACING;
      
      const childResult = calculatePositions(child, level + 1, startX + totalWidth);
      totalWidth += childResult.width;
      childrenCenters.push(childResult.center);
      childrenIds.push(child.id);
    }
    
    // 计算子节点的中心位置作为父节点的位置
    const firstChildX = childrenCenters[0];
    const lastChildX = childrenCenters[childrenCenters.length - 1];
    const centerX = (firstChildX + lastChildX) / 2;
    
    // 存储父节点和其所有子节点的ID信息，用于连接线
    positions[node.id] = { 
      x: centerX, 
      y: level * (NODE_HEIGHT + VERTICAL_SPACING), // 使用固定的垂直间距
      level,
      childrenIds: childrenIds,
      firstChildX: firstChildX,
      lastChildX: lastChildX,
      isLeaf: false // 标记为非叶子节点
    };
    
    // 如果有配偶，在父节点右侧放置配偶节点
    if (node.spouse) {
      positions[node.spouse.id] = {
        x: centerX + NODE_WIDTH + NODE_SPACING / 2, // 添加配偶连接线的宽度
        y: level * (NODE_HEIGHT + VERTICAL_SPACING), // 使用固定的垂直间距
        level,
        isLeaf: false // 配偶节点也标记为非叶子节点
      };
    }
    
    return { width: totalWidth, center: centerX };
  };
  
  calculatePositions(tree);
  return positions;
};

const ClanPage = () => {
  const [activeView, setActiveView] = useState('tree');
  const [scale, setScale] = useState(1);
  const [positions, setPositions] = useState({});
  const [showInfo, setShowInfo] = useState(false);
  
  // 计算节点位置
  useEffect(() => {
    if (activeView === 'tree') {
      const treePositions = calculateTreePositions(familyTreeData);
      setPositions(treePositions);
    }
  }, [activeView]);
  
  const switchView = (viewType) => {
    setActiveView(viewType);
  };

  const handleZoomIn = () => {
    setScale(prev => Math.min(prev + 0.1, 2));
  };

  const handleZoomOut = () => {
    setScale(prev => Math.max(prev - 0.1, 0.5));
  };

  const handleResetZoom = () => {
    setScale(1);
  };
  
  const toggleInfo = () => {
    setShowInfo(!showInfo);
  };
  
  // 渲染族谱节点
  const renderFamilyNode = (node) => {
    if (!node || !positions[node.id]) return null;
    
    const pos = positions[node.id];
    const isCurrent = node.isCurrent;
    const isMale = node.gender === 'male';
    const isLeafNode = pos.isLeaf; // 判断是否为叶子节点
    
    const nodeStyle = {
      position: 'absolute',
      left: `${pos.x - NODE_WIDTH/2}px`,
      top: `${pos.y}px`,
      width: `${NODE_WIDTH}px`,
      height: `${NODE_HEIGHT}px`,
      textAlign: 'center',
      zIndex: 2
    };
    
    const borderColor = isMale ? '#3b82f6' : '#ec4899';
    const borderWidth = isCurrent ? '3px' : '2px';
    
    // 为有子女的男性节点添加节点下方向下的20px线段
    const bottomConnectorLineStyle = isMale && !isLeafNode ? {
      position: 'absolute',
      left: '50%',
      bottom: '-20px', // 从节点底部向下延伸20px
      width: '2px',
      height: '20px',
      backgroundColor: '#a0aec0',
      transform: 'translateX(-50%)' // 确保水平居中
    } : null;
    
    return (
      <div key={node.id} style={nodeStyle} className={`family-node ${isMale ? 'male-node' : 'female-node'} ${isCurrent ? 'current-person' : ''}`}>
        <img
          src={node.avatar}
          alt={`${node.name}头像`}
          style={{
            width: `${IMAGE_SIZE}px`,
            height: `${IMAGE_SIZE}px`,
            borderColor: borderColor,
            borderWidth: borderWidth,
            margin: '0 auto'
          }}
          className="rounded-full object-cover mx-auto"
        />
        <p className={`text-xs mt-1 text-center font-${isCurrent ? 'semibold' : 'medium'}`} style={{ marginTop: '3px' }}>
          {node.name}
        </p>
        {isMale && !isLeafNode && <div style={bottomConnectorLineStyle}></div>}
      </div>
    );
  };
  
  // 渲染夫妻连接线
  const renderSpouseConnector = (node) => {
    if (!node || !node.spouse || !positions[node.id] || !positions[node.spouse.id]) return null;
    
    const pos = positions[node.id];
    
    const connectorStyle = {
      position: 'absolute',
      left: `${pos.x + NODE_WIDTH/2}px`,
      top: `${pos.y + NODE_HEIGHT/2}px`,
      width: `${NODE_SPACING}px`,
      height: '2px',
      backgroundColor: '#a0aec0',
      zIndex: 1
    };
    
    return <div key={`${node.id}-${node.spouse.id}-connector`} style={connectorStyle} className="spouse-connector" />;
  };
  
  // 渲染所有子女节点的连接线
  const renderChildrenConnectors = (node) => {
    if (!node || !positions[node.id] || !node.children || node.children.length === 0) return null;
    
    const parentPos = positions[node.id];
    const isParentMale = node.gender === 'male';
    const childrenLevel = parentPos.level + 1;
    const childrenY = childrenLevel * (NODE_HEIGHT + VERTICAL_SPACING) - 20; // 子节点顶部上方20px处
    
    // 如果只有一个子女，不需要水平连接线
    if (node.children.length === 1) {
      // 单个子女的情况，只需要垂直连接线
      const childPos = positions[node.children[0].id];
      if (!childPos) return null;
      
      const verticalLineStyle = {
        position: 'absolute',
        left: `${parentPos.x}px`, // 从父节点中心垂直向下
        top: `${parentPos.y + NODE_HEIGHT + (isParentMale ? 20 : 0)}px`, // 从父节点底部开始
        width: '2px',
        height: `${childrenY - parentPos.y - NODE_HEIGHT - (isParentMale ? 20 : 0)}px`, // 到子节点顶部上方20px处
        backgroundColor: '#a0aec0',
        zIndex: 1
      };
      
      return (
        <div 
          key={`${node.id}-single-child-connector`} 
          style={verticalLineStyle}
          className="parent-vertical-connector"
        />
      );
    }
    
    // 获取最左边和最右边子女的x坐标
    const firstChildX = parentPos.firstChildX;
    const lastChildX = parentPos.lastChildX;
    
    // 兄弟姐妹之间的水平连接线
    const horizontalLineStyle = {
      position: 'absolute',
      left: `${firstChildX}px`,
      top: `${childrenY}px`,
      width: `${lastChildX - firstChildX}px`,
      height: '2px',
      backgroundColor: '#a0aec0',
      zIndex: 1
    };
    
    // 从父节点到水平线的垂直连接线
    const verticalLineStyle = {
      position: 'absolute',
      left: `${parentPos.x}px`,
      top: `${parentPos.y + NODE_HEIGHT + (isParentMale ? 20 : 0)}px`,
      width: '2px',
      height: `${childrenY - parentPos.y - NODE_HEIGHT - (isParentMale ? 20 : 0)}px`,
      backgroundColor: '#a0aec0',
      zIndex: 1
    };
    
    return (
      <React.Fragment key={`${node.id}-children-connectors`}>
        <div style={horizontalLineStyle} className="siblings-horizontal-connector" />
        <div style={verticalLineStyle} className="parent-vertical-connector" />
      </React.Fragment>
    );
  };
  
  // 渲染子女节点的垂直连接线
  const renderChildVerticalConnectors = (parent) => {
    if (!parent || !positions[parent.id] || !parent.children) return null;
    
    const parentPos = positions[parent.id];
    const childrenLevel = parentPos.level + 1;
    const childrenY = childrenLevel * (NODE_HEIGHT + VERTICAL_SPACING) - 20; // 子节点顶部上方20px处
    
    return parent.children.map(child => {
      const childPos = positions[child.id];
      if (!childPos) return null;
      
      // 从水平线到子女节点的垂直连接线
      const childVerticalLineStyle = {
        position: 'absolute',
        left: `${childPos.x}px`,
        top: `${childrenY}px`,
        width: '2px',
        height: '20px', // 连接到子节点顶部
        backgroundColor: '#a0aec0',
        zIndex: 1
      };
      
      return (
        <div 
          key={`${parent.id}-${child.id}-vertical-connector`} 
          style={childVerticalLineStyle}
          className="child-vertical-connector"
        />
      );
    });
  };
  

  
  // 递归渲染族谱树
  const renderTree = (node) => {
    if (!node) return null;
    
    // 渲染当前节点及其配偶
    const elements = [
      renderFamilyNode(node),
      node.spouse && renderFamilyNode(node.spouse),
      node.spouse && renderSpouseConnector(node)
    ];
    
    // 渲染子节点及连接线
    if (node.children && node.children.length > 0) {
      // 添加子女之间的连接线
      elements.push(renderChildrenConnectors(node));
      
      // 添加从水平线到每个子女的垂直连接线
      elements.push(renderChildVerticalConnectors(node));
      
      // 渲染每个子节点
      node.children.forEach(child => {
        elements.push(renderTree(child));
      });
    }
    
    return elements;
  };
  
  // 渲染代际背景块
  const renderGenerationBlocks = () => {
    // 获取最大层级（代际）
    const maxLevel = Object.values(positions).reduce((max, pos) => Math.max(max, pos.level), 0);
    
    // 创建代际块数组
    const generationBlocks = [];
    
    // 为每个代际创建背景块
    for (let level = 0; level <= maxLevel; level++) {
      // 计算该代际的所有节点
      const nodesInGeneration = Object.entries(positions)
        .filter(([_, pos]) => pos.level === level)
        .map(([id, _]) => id);
      
      const generationY = level * (NODE_HEIGHT + VERTICAL_SPACING);
      
      generationBlocks.push(
        <div 
          key={`generation-${level}`} 
          className="generation-block"
          style={{
            position: 'absolute',
            top: `${generationY - 10}px`, // 稍微上移一点，为边框留空间
            left: '0',
            right: '0',
            height: `${NODE_HEIGHT + 20}px`, // 高度包含节点高度加上一些额外空间
            border: '1px dashed #a0aec0',
            borderRadius: '8px',
            backgroundColor: 'rgba(226, 232, 240, 0.3)', // 淡蓝色背景
            zIndex: 0, // 确保在连接线和节点下方
            padding: '5px',
            display: 'flex',
            alignItems: 'center'
          }}
        >
          <div className="generation-label" style={{ 
            position: 'absolute', 
            left: '10px',
            top: '-10px',
            backgroundColor: 'white',
            padding: '0 5px',
            fontSize: '12px',
            color: '#4a5568'
          }}>
            第{level + 1}代 ({nodesInGeneration.length}人)
          </div>
        </div>
      );
    }
    
    return generationBlocks;
  };
  
  return (
    <div className="p-4">
      <div className="bg-white rounded-lg shadow-md p-4 mb-4">
        <div className="flex justify-between items-center mb-2">
          <h2 className="text-xl font-semibold text-gray-800">族谱视图</h2>
          <button 
            onClick={toggleInfo} 
            className="text-blue-500 text-sm hover:underline"
          >
            {showInfo ? '隐藏算法说明' : '查看算法说明'}
          </button>
        </div>
        
        {showInfo && (
          <div className="bg-blue-50 p-3 rounded-md mb-4 text-sm">
            <h3 className="font-medium text-blue-700 mb-1">实现算法说明</h3>
            <ul className="list-disc list-inside text-blue-800 space-y-1">
              <li>采用<strong>子女位置决定父亲位置</strong>的算法实现</li>
              <li>父节点的横坐标为其所有子节点横坐标的平均值</li>
              <li>节点宽度为80px，节点高度为85px，子节点间距为20px，夫妻节点间距为20px</li>
              <li>使用后序遍历计算位置：先计算最底层子节点位置，再向上计算父节点</li>
              <li>叶子节点（无子女的节点）直接从左到右排列</li>
              <li>父节点的纵坐标由层级确定，同一代的节点在同一水平线上</li>
              <li>同一父亲的子女节点通过水平线连接，形成统一的连接结构</li>
            </ul>
          </div>
        )}
        
        <div className="flex justify-around mb-4">
          <button
            className={`px-4 py-2 rounded-md text-sm ${
              activeView === 'tree' ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700'
            }`}
            onClick={() => switchView('tree')}
          >
            树结构
          </button>
          <button
            className={`px-4 py-2 rounded-md text-sm ${
              activeView === 'table' ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700'
            }`}
            onClick={() => switchView('table')}
          >
            年代表格
          </button>
        </div>
        
        {/* 族谱树结构 */}
        {activeView === 'tree' && (
          <div className="clan-view">
            {/* 缩放控制 */}
            <div className="zoom-controls flex justify-center space-x-4 mb-4">
              <button 
                onClick={handleZoomOut}
                className="px-3 py-1 bg-gray-200 rounded-md text-gray-700 hover:bg-gray-300"
              >
                -
              </button>
              <button 
                onClick={handleResetZoom}
                className="px-3 py-1 bg-gray-200 rounded-md text-gray-700 hover:bg-gray-300"
              >
                重置
              </button>
              <button 
                onClick={handleZoomIn}
                className="px-3 py-1 bg-gray-200 rounded-md text-gray-700 hover:bg-gray-300"
              >
                +
              </button>
              <span className="text-sm text-gray-500 py-1">
                {Math.round(scale * 100)}%
              </span>
            </div>

            {/* 族谱树容器 - 设置最大高度和滚动条 */}
            <div className="family-tree-wrapper overflow-auto border border-gray-200 rounded-lg" style={{ maxHeight: "70vh" }}>
                              <div 
                className="family-tree-container" 
                style={{
                  transform: `scale(${scale})`,
                  transformOrigin: 'center top',
                  transition: 'transform 0.3s ease',
                  position: 'relative',
                  minHeight: '60vh',
                  padding: '30px',
                  width: 'fit-content',  // 确保容器宽度适应内容
                  margin: '30px auto'    // 水平居中
                }}
              >
                {Object.keys(positions).length > 0 && renderGenerationBlocks()}
                {Object.keys(positions).length > 0 && renderTree(familyTreeData)}
              </div>
            </div>
            
            {/* 
            <div className="mt-4 text-xs text-gray-500">
              <p>注：蓝色边框表示男性，粉色边框表示女性，粗边框表示当前本人。父亲节点的位置由其所有子女节点位置的平均值确定。</p>
            </div>
            */}
          </div>
        )}
        
        {/* 族谱年代表格 */}
        {activeView === 'table' && (
          <div className="clan-view">
            <h3 className="text-lg font-medium mb-2">按年代显示</h3>
            <div className="bg-gray-50 p-3 rounded-md mb-2">
              <h4 className="font-semibold text-gray-700">
                1990年代前期 (1990-1993)
              </h4>
              <ul className="list-disc list-inside text-sm text-gray-600 mt-1">
                <li>张大明 (1991)</li>
                <li>李小红 (1993)</li>
              </ul>
            </div>
            
            <div className="bg-gray-50 p-3 rounded-md mb-2">
              <h4 className="font-semibold text-gray-700">
                1990年代中期 (1994-1996)
              </h4>
              <ul className="list-disc list-inside text-sm text-gray-600 mt-1">
                <li>王建国 (1995)</li>
              </ul>
            </div>
            
            <div className="bg-gray-50 p-3 rounded-md">
              <h4 className="font-semibold text-gray-700">
                1990年代后期 (1997-1999)
              </h4>
              <ul className="list-disc list-inside text-sm text-gray-600 mt-1">
                <li>赵丽丽 (1998)</li>
              </ul>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ClanPage; 