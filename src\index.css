@tailwind base;
@tailwind components;
@tailwind utilities;

/* 家谱系统移动端样式 */
/* 使用Tailwind CSS，这里只需要很少的自定义样式 */

.family-view, .clan-view {
  width: 100%;
}

/* 族谱树结构样式 */
.family-tree-wrapper {
  width: 100%;
  overflow-x: auto;
  /* overflow-y: hidden; */
  padding-bottom: 20px;
}

.family-tree-container {
  padding: 20px 0;
  position: relative;
  min-width: 100%;
  width: fit-content;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 家族节点基础样式 */
.family-node {
  position: relative;
  width: 90px;
  padding: 8px;
  border-radius: 8px;
  background-color: #f9fafb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  z-index: 2;
}

.male-node {
  border-left: 3px solid #3b82f6;
}

.female-node {
  border-left: 3px solid #ec4899;
}

.current-person {
  box-shadow: 0 2px 6px rgba(59, 130, 246, 0.3);
}

/* 夫妻连接线样式 */
.spouse-container {
  display: flex;
  align-items: center;
  position: relative;
}

.spouse-connector {
  width: 10px;
  height: 2px;
  background-color: #a0aec0;
  margin: 0;
}

/* 父子连接线样式 */
.parent-child-connector {
  position: absolute;
  width: 2px;
  background-color: #a0aec0;
  z-index: 1;
}

/* 族谱树结构 */
.family-tree {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 40px; /* 代际之间的垂直间距 */
  position: relative;
}

/* 代际行 */
.generation-row {
  display: flex;
  justify-content: center;
  width: 100%;
  position: relative;
}

/* 家庭单元 */
.family-unit {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  margin-left: 20px;
}

/* 父母容器 */
.parents {
  margin-bottom: 0;
  position: relative;
}

/* 子女容器 */
.children {
  display: flex;
  justify-content: center;
  gap: 40px; /* 兄弟姐妹之间的水平间距 */
  position: relative;
}

/* 根据子女数量调整间距 */
.children-1 {
  /* 单个子女，居中对齐 */
  gap: 0;
}

.children-2 {
  /* 两个子女，适当间距 */
  gap: 20px;
}

.children-3 {
  /* 三个子女，较小间距 */
  gap: 60px;
}

.children-4 {
  /* 四个子女，更小间距 */
  gap: 40px;
}

/* 移动端响应式调整 */
@media (max-width: 640px) {
  .family-tree {
    gap: 30px;
  }
  
  .family-node {
    width: 80px;
  }
  
  .family-node img {
    width: 50px;
    height: 50px;
  }
  
  .spouse-connector {
    width: 20px;
  }
  
  .children {
    gap: 20px;
  }
  
  .children-2 {
    gap: 20px;
  }
  
  .children-3 {
    gap: 30px;
  }
  
  .children-4 {
    gap: 20px;
  }
}

/* 缩放控制样式 */
.zoom-controls button {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

/* 自定义样式，用于覆盖或补充 Tailwind CSS */
.tab-content.active {
  display: block;
}
.tab-content {
  display: none;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

