import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
// 移除 App.css 的导入

// Import pages and components
import Navigation from './components/Navigation';
import HomePage from './pages/HomePage';
import ClanPage from './pages/ClanPage';
import FamilyPage from './pages/FamilyPage';
import FamilyTreePage from './pages/FamilyTreePage';

function App() {
  return (
    <Router>
      <div className="bg-gray-100 min-h-screen flex flex-col">
        <main className="flex-1 overflow-y-auto pb-16">
          <Routes>
            <Route path="/" element={<HomePage />} />
            <Route path="/clan" element={<ClanPage />} />
            <Route path="/family" element={<FamilyPage />} />
            <Route path="/family-tree" element={<FamilyTreePage />} />
          </Routes>
        </main>
        <Navigation />
      </div>
    </Router>
  );
}

export default App;
