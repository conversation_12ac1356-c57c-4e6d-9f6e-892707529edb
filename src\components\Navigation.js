import React from 'react';
import { NavLink } from 'react-router-dom';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faUser, faTree, faSitemap, faProjectDiagram } from '@fortawesome/free-solid-svg-icons';

const Navigation = () => {
  return (
    <nav className="fixed bottom-0 left-0 right-0 bg-white shadow-lg z-50">
      <div className="flex justify-around items-center h-16">
        <NavLink
          to="/"
          className={({ isActive }) => 
            `flex flex-col items-center ${isActive ? 'text-blue-600' : 'text-gray-600 hover:text-blue-600'}`
          }
          end
        >
          <FontAwesomeIcon icon={faUser} className="text-xl" />
          <span className="text-xs mt-1">我的</span>
        </NavLink>
        
        <NavLink
          to="/clan"
          className={({ isActive }) => 
            `flex flex-col items-center ${isActive ? 'text-blue-600' : 'text-gray-600 hover:text-blue-600'}`
          }
        >
          <FontAwesomeIcon icon={faTree} className="text-xl" />
          <span className="text-xs mt-1">族谱</span>
        </NavLink>
        
        <NavLink
          to="/family"
          className={({ isActive }) => 
            `flex flex-col items-center ${isActive ? 'text-blue-600' : 'text-gray-600 hover:text-blue-600'}`
          }
        >
          <FontAwesomeIcon icon={faSitemap} className="text-xl" />
          <span className="text-xs mt-1">家谱</span>
        </NavLink>
        
        <NavLink
          to="/family-tree"
          className={({ isActive }) => 
            `flex flex-col items-center ${isActive ? 'text-blue-600' : 'text-gray-600 hover:text-blue-600'}`
          }
        >
          <FontAwesomeIcon icon={faProjectDiagram} className="text-xl" />
          <span className="text-xs mt-1">自定义树</span>
        </NavLink>
      </div>
    </nav>
  );
};

export default Navigation; 